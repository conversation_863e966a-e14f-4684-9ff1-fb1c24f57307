#from charset_normalizer import md__mypyc
import gui.login
from tkinter import *


def page():
    window = Tk()
    img = PhotoImage(file="icon.png")
    window.iconphoto(False, img)
    try:
        gui.login.LoginPage(window)
    except Exception as e:
        print(e)
    #expire = 'no expires'
    #GUI_ROKSCAN.ROKSCAN(window, expire)
    window.mainloop()


if __name__ == '__main__':
    try:
        page()
    except Exception as e:
        print(e)
