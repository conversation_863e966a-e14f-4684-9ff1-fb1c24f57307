import discord
import asyncio
from time import sleep
import handle_responses
import time
import json
import concurrent.futures
import threading
import solve_captcha
import titles.title
import cv2
def reboot_function(message):
    threading.Thread(target=titles.title.reboot).start()
def reboot_thread(message):
    t = threading.Thread(target=reboot_function, args=(message,))
    t.start()

def check():
    if running == True:
        return True
    elif running == False:
        return False
def check_duke():
    if duke_working == True:
        return True
    elif duke_working == False:
        return False
def check_justice():
    if justice_working == True:
        return True
    elif justice_working == False:
        return False
def check_architect():
    if architect_working == True:
        return True
    elif architect_working == False:
        return False
def check_scientist():
    if scientist_working == True:
        return True
    elif scientist_working == False:
        return False
running = True
duke_working = True
justice_working = True
architect_working = True
scientist_working = True
d = json.load(open('config.json'))
d['working'] = False
with open('config.json', 'w') as f:
    json.dump(d, f, indent=1)
def check_captchas():
    titles.title.start_adb_server()
    print('Connecting to adb')
    titles.title.connect_adb()
    d = json.load(open('config.json'))
    working = d['working']
    while working is not True:
        print('Checking captchas while sleeping')
        d = json.load(open('config.json'))
        ip = d['ip']
        port = d['port']
        working = d['working']
        #print(working)
        # Call your captcha solver function here
        if working is not True:
            solve_captcha.check_captcha(ip,port)
        # Wait for 5 seconds before checking again
        time.sleep(10)

def start_captcha_check():
    thread = threading.Thread(target=check_captchas)
    thread.start()

async def run_discord_bot():

    duke = ['duke','dk', 'd','duk','dukk','dukee','dukes','duque','duc','dukie']
    justice = ['justice', 'j','just','justi','justic','justis','jstice','justce','jst','jutice','justise','justcie']
    architect = ['architect','arch','arc','a','arche','arhit','archi','archit','architec','architectu','architct','archiet']
    scientist = ['scientist','sci','s','scien','scienct','scientis','sct']
    TOKEN = 'MTA3NzMyMTgyMTYwODc1OTI5Ng.GRftIx.2f8EqwQAjJAgQAyxhj7SrM8bD9syAU9emByk_U'
    intents = discord.Intents.default()
    intents.message_content = True
    client = discord.Client(intents=intents)
    d = json.load(open('config.json'))
    user_channel = int(d['user_channel'])
    admin_channel = int(d['admin_channel'])
    d['working'] = False
    start_captcha_check()
    with open('config.json', 'w') as f:
        json.dump(d, f, indent=1)
    @client.event
    async def on_ready():
        print(f'{client.user} is now running!')

    cooldowns = {'!title duke': 0, '!title justice': 0,'!title architect': 0,'!title scientist': 0}
    done = {'!title duke': 0, '!title justice': 0,'!title architect': 0,'!title scientist': 0}
    duke_queue = []
    scientist_queue = []
    architect_queue = []
    justice_queue = []
    async def handle_command(to_add):
        #print(to_add)
        current_time = int(time.time())
        cooldown = cooldowns.get(to_add['command'], 0)
        command = to_add['command']
        d = json.load(open('config.json'))
        booster = json.load(open('booster.json'))
        id = to_add['author'].replace('<','').replace('>','').replace('@','')
        individual_times = {'!title duke': 'duke_time', '!title justice': 'justice_time','!title architect': 'architect_time','!title scientist': 'scientist_time'}

        if id in booster and d['booster'] == True:
            try:
                customized_cd = d['booster_time']
            except:
                customized_cd = d['waiting_time']
                channel_id = client.get_channel(user_channel)
                description = f'You are in the booster list, but your leadership needs to set the booster time'
                embed = discord.Embed(
                    description=description,
                    color=discord.Color.red())
                await channel_id.send(embed=embed)
        else:
            try:
                print(d[individual_times[to_add[command]]])
                customized_cd = d[individual_times[to_add[command]]]
            except Exception as e:
                #print(e)
                try:
                    customized_cd = d['waiting_time']
                except:
                    d['waiting_time'] = 0
                    customized_cd = 0

        if current_time < cooldown:
            # Command is still on cooldown, add it to the queue

            if command == '!title duke':
                duke_queue.insert(0,to_add)
            if command == '!title justice':
                justice_queue.insert(0,to_add)
            if command == '!title architect':
                architect_queue.insert(0,to_add)
            if command == '!title scientist':
                scientist_queue.insert(0,to_add)
            return
        else:

            channel_id = client.get_channel(user_channel)
            # Command is not on cooldown, execute it
            if to_add['command'] == '!title duke':
                d = json.load(open('config.json'))
                #print(d['working'])
                if d['working'] == True:
                    duke_queue.insert(0,to_add)
                else:
                    next = to_add['author']
                    description = f'{next} You are the next in the ***duke*** queue, get ready'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await channel_id.send(embed=embed)
            elif to_add['command'] == '!title justice':
                d = json.load(open('config.json'))
                if d['working'] == True:
                    justice_queue.insert(0,to_add)
                else:
                    next = to_add['author']
                    description = f'{next} You are the next in the ***justice*** queue, get ready'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await channel_id.send(embed=embed)
            elif to_add['command'] == '!title architect':
                d = json.load(open('config.json'))
                if d['working'] == True:
                    architect_queue.insert(0,to_add)
                else:
                    next = to_add['author']
                    description = f'{next} You are the next in the ***architect*** queue, get ready'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await channel_id.send(embed=embed)
            elif to_add['command'] == '!title scientist':
                d = json.load(open('config.json'))
                if d['working'] == True:
                    scientist_queue.insert(0,to_add)
                else:
                    next = to_add['author']
                    description = f'{next} You are the next in the ***scientist*** queue, get ready'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await channel_id.send(embed=embed)
            d = json.load(open('config.json'))

            if d['working'] is not True:
                print('Ready to prepare for the title')
                done[to_add['command']] = to_add
                cooldowns[to_add['command']] = current_time + int(customized_cd)
                await execute_command(to_add['command'], to_add['params'])



    async def execute_command(command, message):
        # Your code to execute the command goes here
        print(f'Executing {command} with params {message}')
        # Sleep for a while to simulate processing time
        channel_id = client.get_channel(user_channel)

        loop = asyncio.get_event_loop()

        with concurrent.futures.ThreadPoolExecutor() as pool:
            response = await loop.run_in_executor(pool, handle_responses.handle_response, message.content,
                                                  message.author.mention)

        if response is not None and 'ERROR' in response:
            cooldowns[command] = 0
        if response is not None and "ERROR: I couldn't find your city" in response:
            description = response
            embed = discord.Embed(
                description=description,
                color=discord.Color.red())
            #await channel_id.send(embed=embed)
            # Add the image as an attachment to the embed

            file = discord.File('city_not_found.png', filename="image.png")
            embed.set_image(url="attachment://image.png")  # The 'url' should match the filename used above

            # Assuming 'channel_id' is a valid channel to send the embed
            await channel_id.send(embed=embed, file=file)
        else:
            description = response
            embed = discord.Embed(
                description=description,
                color=discord.Color.red())
            await channel_id.send(embed=embed)

        d = json.load(open('config.json'))
        d['working'] = False
        with open('config.json', 'w') as f:
            json.dump(d, f, indent=1)
        start_captcha_check()

    async def process_queue():
        while True:

            if len(duke_queue)>0:
                # Sort the command queue by timestamp
                duke_queue.sort(key=lambda x: x['timestamp'])
                to_add = duke_queue.pop(0)
                await handle_command(to_add)
            if len(justice_queue)>0:
                justice_queue.sort(key=lambda x: x['timestamp'])
                to_add = justice_queue.pop(0)
                await handle_command(to_add)
            if len(architect_queue)>0:
                architect_queue.sort(key=lambda x: x['timestamp'])
                to_add = architect_queue.pop(0)
                await handle_command(to_add)
            if len(scientist_queue)>0:
                scientist_queue.sort(key=lambda x: x['timestamp'])
                to_add = scientist_queue.pop(0)
                await handle_command(to_add)
            await asyncio.sleep(1)
    @client.event
    async def on_message(message):
        global running
        global duke_working, architect_working,justice_working,scientist_working

        # Make sure bot doesn't get stuck in an infinite loop
        if message.author == client.user:
            return

        # Get data about the user
        username = str(message.author)
        global user_message
        user_message = str(message.content)
        channel = str(message.channel)


        if message.channel.id == user_channel:
            ##Writing on json datas
            blacklist = json.load(open('blacklist.json'))
            print(f"{username} said: '{user_message}' ({channel})")
            sleep(0.5)
            condition = check()
            print('Checking if bot is online')
            if condition == False and message.content.startswith('!'):
                print('Bot is offline')
                channel_id = client.get_channel(user_channel)
                d = json.load(open('config.json'))
                reason = d['global_msg']
                description = f'Title giver bot is offline: refer to your leadership about the reason. REASON: {reason}'
                embed = discord.Embed(
                    description=description,
                    color=discord.Color.red())
                await message.channel.send(embed=embed)

            elif str(message.author.id) in blacklist:
                print('Bot is online')
                print('User is blacklisted')
                member_name = message.author.mention
                description = f'You are blacklisted{member_name}'
                embed = discord.Embed(
                    description=description,
                    color=discord.Color.red())
                await message.channel.send(embed=embed)

            elif message.content.startswith('!title'):
                print('Bot is online: ready to go')
                config = json.load(open('config.json'))
                #Whitelisted users
                whitelist = json.load(open('whitelist.json'))
                user_id = str(message.author.id)
                whitelisted = False
                if user_id in whitelist:
                    print('User whitelisted')
                    whitelisted = True
                # Split the message content into command and parameters
                split_message = message.content.split(' ')
                command = split_message[0]
                params = split_message[1:]
                if command == '!title' and len(params) > 0:

                    if params[0].lower() in duke:
                        ######
                        duke_working = check_duke()
                        if duke_working == True:
                            print('Title: Duke')
                            to_add = {}
                            to_add['author'] = message.author.mention
                            to_add['command'] = '!title duke'
                            to_add['params'] = message
                            current_time = time.time()
                            to_add['timestamp'] = current_time
                            print(to_add)
                            not_in_queue = False
                            channel_id = client.get_channel(user_channel)

                            for in_queue in duke_queue:
                                if str(message.author.mention) == str(in_queue['author']):
                                    description = f'{message.author.mention}You are already in the **duke** queue'
                                    embed = discord.Embed(
                                        description=description,
                                        color=discord.Color.red())
                                    await message.channel.send(embed=embed)
                                    not_in_queue = True
                                    break

                            if not_in_queue == False:
                                working = config['working']
                                try:
                                    waiting_time =config['duke_time']
                                except:
                                    waiting_time = config['waiting_time']
                                current_time = int(time.time())
                                cooldown = cooldowns.get('!title duke', 0)
                                if len(duke_queue) < 1 and (current_time > cooldown and working == False):
                                        pass
                                else:
                                    description = f'{message.author.mention} You have been added successfully into **duke** queue.\n' \
                                                  f'Your current position is **{len(duke_queue) + 1}**\n' \
                                                  f'***Expected time until your turn: {str(((len(duke_queue)+1) * waiting_time) / 60)} minutes***\n' \
                                                  f'If you want to know your position into the queue, please type ***!queue***.\n' \
                                                  f'If you typed wrong coordinates, please use ***!remove [TITLE_NAME]*** to remove you request from the queue and to allow you to make it again.'
                                    embed = discord.Embed(
                                        description=description,
                                        color=discord.Color.red())
                                    await message.channel.send(embed=embed)
                                if whitelisted == True:
                                    duke_queue.insert(0,to_add)
                                else:
                                    duke_queue.append(to_add)
                        else:
                            d = json.load(open('config.json'))
                            reason = d['duke_msg']
                            description = f'{message.author.mention} **duke** title is disabled. REASON: {reason}'
                            embed = discord.Embed(
                                description=description,
                                color=discord.Color.red())
                            await message.channel.send(embed=embed)
                    elif params[0].lower() in justice:
                        justice_working = check_justice()
                        if justice_working == True:
                            print('Title: Justice')
                            to_add = {}
                            to_add['author'] = message.author.mention
                            to_add['command'] = '!title justice'
                            to_add['params'] = message
                            current_time = time.time()
                            to_add['timestamp'] = current_time
                            not_in_queue = False
                            channel_id = client.get_channel(user_channel)

                            for in_queue in justice_queue:
                                if str(message.author.mention) == str(in_queue['author']):
                                    description = f'{message.author.mention}You are already in the **justice** queue'
                                    embed = discord.Embed(
                                        description=description,
                                        color=discord.Color.red())
                                    await message.channel.send(embed=embed)
                                    not_in_queue = True
                                    break
                            if not_in_queue == False:
                                try:
                                    waiting_time =config['justice_time']
                                except:
                                    waiting_time = config['waiting_time']
                                working = config['working']
                                print(working)
                                current_time = int(time.time())
                                cooldown = cooldowns.get('!title justice', 0)
                                if len(justice_queue) < 1 and (current_time > cooldown and working == False):
                                    print(current_time - cooldown)
                                    pass
                                else:
                                    description = f'{message.author.mention} You have been added successfully into **justice** queue.\n' \
                                                  f'Your current position is **{len(justice_queue) + 1}**\n' \
                                                  f'***Expected time until your turn: {str(((len(justice_queue)+1)*waiting_time)/60)} minutes***\n' \
                                                  f'If you want to know your position into the queue, please type ***!queue***.\n' \
                                                  f'If you typed wrong coordinates, please use ***!remove [TITLE_NAME]*** to remove you request from the queue and to allow you to make it again.'
                                    embed = discord.Embed(
                                        description=description,
                                        color=discord.Color.red())
                                    await message.channel.send(embed=embed)
                                if whitelisted == True:
                                    justice_queue.insert(0,to_add)
                                else:

                                    try:
                                        justice_queue.append(to_add)
                                    except Exception as e:
                                        print(e)
                        else:
                            d = json.load(open('config.json'))
                            reason = d['justice_msg']
                            description = f'{message.author.mention} **justice** is disabled. REASON: {reason}'
                            embed = discord.Embed(
                                description=description,
                                color=discord.Color.red())
                            await message.channel.send(embed=embed)
                    elif params[0].lower() in architect:
                        architect_working = check_architect()
                        if architect_working == True:
                            print('Title: Architect')
                            to_add = {}
                            to_add['author'] = message.author.mention
                            to_add['command'] ='!title architect'
                            to_add['params'] = message
                            current_time = time.time()
                            to_add['timestamp'] = current_time
                            not_in_queue = False
                            channel_id = client.get_channel(user_channel)
                            for in_queue in architect_queue:
                                if str(message.author.mention) == in_queue['author']:
                                    description = f'{message.author.mention}You are already in the **justice** queue'
                                    embed = discord.Embed(
                                        description=description,
                                        color=discord.Color.red())
                                    await message.channel.send(embed=embed)
                                    not_in_queue = True
                                    break

                            if not_in_queue == False:
                                try:
                                    waiting_time =config['architect_time']
                                except:
                                    waiting_time = config['waiting_time']

                                working = config['working']
                                current_time = int(time.time())
                                cooldown = cooldowns.get('!title architect', 0)
                                if len(architect_queue) < 1 and (current_time > cooldown and working == False):
                                        pass
                                else:
                                    description = f'{message.author.mention} You have been added successfully into **architect** queue.\n' \
                                                  f'Your current position is **{len(architect_queue) + 1}**\n' \
                                                  f'***Expected time until your turn: {str(((len(architect_queue)+1) * waiting_time) / 60)} minutes***\n' \
                                                  f'If you want to know your position into the queue, please type ***!queue***.\n' \
                                                  f'If you typed wrong coordinates, please use ***!remove [TITLE_NAME]*** to remove you request from the queue and to allow you to make it again.'
                                    embed = discord.Embed(
                                        description=description,
                                        color=discord.Color.red())
                                    await message.channel.send(embed=embed)
                                if whitelisted == True:
                                    architect_queue.insert(0,to_add)
                                else:
                                    architect_queue.append(to_add)
                        else:
                            d = json.load(open('config.json'))
                            reason = d['architect_msg']
                            description = f'{message.author.mention} **architect** is disabled. REASON: {reason}'
                            embed = discord.Embed(
                                description=description,
                                color=discord.Color.red())
                            await message.channel.send(embed=embed)
                    elif params[0].lower() in scientist:
                        scientist_working = check_scientist()
                        if scientist_working == True:
                            print('Title: Scientist')
                            to_add = {}
                            to_add['author'] = message.author.mention
                            to_add['command'] = '!title scientist'
                            to_add['params'] = message
                            current_time = time.time()
                            to_add['timestamp'] = current_time
                            not_in_queue = False
                            channel_id = client.get_channel(user_channel)
                            for in_queue in scientist_queue:
                                if str(message.author.mention) == in_queue['author']:
                                    description = f'{message.author.mention}You are already in the **scientist** queue'
                                    embed = discord.Embed(
                                        description=description,
                                        color=discord.Color.red())
                                    await message.channel.send(embed=embed)
                                    not_in_queue = True
                                    break

                            if not_in_queue == False:
                                try:
                                    waiting_time =config['scientist_time']
                                except:
                                    waiting_time = config['waiting_time']
                                working = config['working']
                                current_time = int(time.time())
                                cooldown = cooldowns.get('!title scientist', 0)
                                if len(scientist_queue) < 1 and (current_time > cooldown and working == False):
                                        pass
                                else:
                                    description = f'{message.author.mention} You have been added successfully into **scientist** queue.\n' \
                                                  f'Your current position is **{len(scientist_queue) + 1}**\n' \
                                                  f'***Expected time until your turn: {str(((len(scientist_queue)+1) * waiting_time) / 60)} minutes***\n' \
                                                  f'If you want to know your position into the queue, please type ***!queue***.\n' \
                                                  f'If you typed wrong coordinates, please use ***!remove [TITLE_NAME]*** to remove you request from the queue and to allow you to make it again.'
                                    embed = discord.Embed(
                                        description=description,
                                        color=discord.Color.red())
                                    await message.channel.send(embed=embed)
                                if whitelisted == True:
                                    scientist_queue.insert(0,to_add)
                                else:
                                    scientist_queue.append(to_add)
                        else:
                            d = json.load(open('config.json'))
                            reason = d['scientist_msg']
                            description = f'{message.author.mention} **scientist** is disabled. REASON: {reason}'
                            embed = discord.Embed(
                                description=description,
                                color=discord.Color.red())
                            await message.channel.send(embed=embed)
            elif user_message == '!done':
                author = message.author.mention
                removed = False
                for key in done.keys():
                    try:
                        if done[key]['author'] == author:
                            cooldowns[key] = 0
                            removed = True
                    except Exception as e:
                        print(e)

                if removed is True:
                    description = f'{message.author.mention} Thank you for your patience, !done received correctly.'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
            elif user_message == '!help':
                description = "***!title duke [KD NUMBER] [X] [Y]***\n" \
                              "***!title justice [KD NUMBER] [X] [Y]***\n" \
                              "***!title architect [KD NUMBER] [X] [Y]***\n" \
                              "***!title scientist [KD NUMBER] [X] [Y]***\n" \
                              "Instead of [KD NUMBER] you can use **hk** or **lk**\n" \
                              "If you do the command one time, specifying the kingdom number and the coordinates, if you need title one more time and you **DIDN'T** change your location, you can type simply\n" \
                              "***!title duke***\n" \
                              "***!title justice***\n" \
                              "***!title architect***\n" \
                              "***!title scientist***\n" \
                              "Note: when you are done with the title, please type ***!done***\n" \
                              "If you do change your location, type again the full command\n" \
                              "\n" \
                              "***!queue***: use this command to know in which position of the queue you are\n" \
                              "***!remove [TITLE_NAME]***: use this command to remove a title request from the queue.\n" \
                              "Example: If you do type wrong coordinates, use **!remove [TITLE_NAME]** to cancel the request and that will allow you to make the correct request"
                embed = discord.Embed(title='User Help',
                    description=description,
                    color=discord.Color.red())
                await message.channel.send(embed=embed)
            elif user_message == '!queue':
                queue_check_scientist= False
                queue_check_architect= False
                queue_check_justice= False
                queue_check_duke = False
                author = message.author.mention
                for requests in duke_queue:

                    if requests['author'] == author:
                        queue_check_duke = True
                        position = duke_queue.index(requests)
                        description = f'{message.author.mention} Your position in the **duke** queue is: **{position+1}**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                if queue_check_duke == False:
                    description = f'{message.author.mention} You are not in the **duke** queue'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                for requests in justice_queue:
                    if requests['author'] == author:
                        queue_check_justice = True
                        position = justice_queue.index(requests)
                        description = f'{message.author.mention} Your position in the **justice** queue is: **{position+1}**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                if queue_check_justice == False:
                    description = f'{message.author.mention} You are not in the **justice** queue'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                for requests in architect_queue:
                    if requests['author'] == author:
                        queue_check_architect = True
                        position = architect_queue.index(requests)
                        description = f'{message.author.mention} Your position in the **architect** queue is: **{position+1}**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                if queue_check_architect == False:
                    description = f'{message.author.mention} You are not in the **architect** queue'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                for requests in scientist_queue:
                    if requests['author'] == author:
                        queue_check_scientist = True
                        position = scientist_queue.index(requests)
                        description = f'{message.author.mention} Your position in the **scientist** queue is: **{position+1}**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                if queue_check_scientist == False:
                    description = f'{message.author.mention} You are not in the **scientist** queue'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
            elif user_message.startswith('!remove'):
                removed = False
                author = message.author.mention
                selected_queue = user_message.split(' ')[1]
                if selected_queue.lower() == 'duke':
                    for requests in duke_queue:
                        if requests['author'] == author:
                            removed = True
                            position = duke_queue.index(requests)
                            duke_queue.pop(position)
                            description = f'{message.author.mention} You have been removed from **duke** queue'
                            embed = discord.Embed(
                                description=description,
                                color=discord.Color.red())
                            await message.channel.send(embed=embed)
                elif selected_queue.lower() == 'justice':
                    for requests in justice_queue:
                        if requests['author'] == author:
                            removed = True
                            position = justice_queue.index(requests)
                            justice_queue.pop(position)
                            description = f'{message.author.mention} You have been removed from **justice** queue'
                            embed = discord.Embed(
                                description=description,
                                color=discord.Color.red())
                            await message.channel.send(embed=embed)
                elif selected_queue.lower() == 'architect':
                    for requests in architect_queue:
                        if requests['author'] == author:
                            removed = True
                            position = architect_queue.index(requests)
                            architect_queue.pop(position)
                            description = f'{message.author.mention} You have been removed from **architect** queue'
                            embed = discord.Embed(
                                description=description,
                                color=discord.Color.red())
                            await message.channel.send(embed=embed)
                elif selected_queue.lower() == 'scientist':
                    for requests in scientist_queue:
                        if requests['author'] == author:
                            removed = True
                            position = scientist_queue.index(requests)
                            scientist_queue.pop(position)
                            description = f'{message.author.mention} You have been removed from **scientist** queue'
                            embed = discord.Embed(
                                description=description,
                                color=discord.Color.red())
                            await message.channel.send(embed=embed)
                if removed == False:
                    description = f'{message.author.mention} You are not in the {selected_queue} queue'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
        try:
            if message.channel.id == admin_channel:
                print('Handling the request')
                channel_id = client.get_channel(admin_channel)
                d = json.load(open('config.json'))
                #d['hosted']
                if message.content.startswith('!reboot') and d['hosted'] is True:
                    d = json.load(open('config.json'))
                    d['working'] = True
                    with open('config.json', 'w') as f:
                        json.dump(d, f, indent=1)
                    description = f'Rebooting'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                    reboot_thread(message)
                    description = f'Rebooted'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                if message.content.startswith('!set_time'):
                    split_message = message.content.split(' ')

                    params = split_message[1]
                    if len(params) != 0:
                        d = json.load(open('config.json'))
                        d['waiting_time'] = int(params)*60
                        with open('config.json', 'w') as f:
                            json.dump(d, f, indent=1)
                        description = f'New waiting time set to:**{str(params)}** min'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)

                elif message.content.startswith('!turn_off'):
                    d = json.load(open('config.json'))
                    d['duke_msg'] = ""
                    d['justice_msg'] = ""
                    d['architect_msg'] = ""
                    d['scientist_msg'] = ""
                    d['global_msg'] = ""
                    with open('config.json', 'w') as f:
                        json.dump(d, f, indent=1)
                    if 'duke' in message.content.lower():
                        d = json.load(open('config.json'))
                        d['duke_msg'] = message.content.split(" ")[-1].replace("duke","")
                        with open('config.json', 'w') as f:
                            json.dump(d, f, indent=1)
                        duke_working = False
                        description = f'**Duke** is off, to turn it on digit **!turn_on duke**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                    elif 'architect' in message.content.lower():
                        d = json.load(open('config.json'))
                        d['architect_msg'] = message.content.split(" ")[-1].replace("architect", "")
                        with open('config.json', 'w') as f:
                            json.dump(d, f, indent=1)
                        architect_working = False
                        description = f'**Architect** is off, to turn it on digit **!turn_on architect**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                    elif 'justice' in message.content.lower():
                        d = json.load(open('config.json'))
                        d['justice_msg'] = message.content.split(" ")[-1].replace("justice", "")
                        with open('config.json', 'w') as f:
                            json.dump(d, f, indent=1)
                        justice_working = False
                        description = f'**Justice** is off, to turn it on digit **!turn_on justice**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                    elif 'scientist' in message.content.lower():
                        d = json.load(open('config.json'))
                        d['scientist_msg'] = message.content.split(" ")[-1].replace("scientist", "")
                        with open('config.json', 'w') as f:
                            json.dump(d, f, indent=1)
                        scientist_working = False
                        description = f'**scientist** is off, to turn it on digit **!turn_on scientist**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                    else:
                        d = json.load(open('config.json'))
                        try:
                            d['global_msg'] = message.content.split(" ")[-1].replace("!turn_off", "")
                        except:
                            d['global_msg'] = ""
                        with open('config.json', 'w') as f:
                            json.dump(d, f, indent=1)
                        running = False
                        description = f'Bot is off, to turn it on digit **!turn_on**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)

                elif message.content.startswith('!turn_on'):
                    if 'duke' in message.content.lower():
                        duke_working = True
                        description = f'**Duke** is on, to turn it off digit **!turn_off duke**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                    elif 'architect' in message.content.lower():
                        architect_working = True
                        description = f'**Architect** is on, to turn it off digit **!turn_off architect**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                    elif 'justice' in message.content.lower():
                        justice_working = True
                        description = f'**Justice** is on, to turn it off digit **!turn_off justice**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                    elif 'scientist' in message.content.lower():
                        scientist_working = True
                        description = f'**scientist** is on, to turn it off digit **!turn_off scientist**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                    else:
                        running = True
                        description = f'Bot is on, to turn it off digit **!turn_off**'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)

                elif message.content.startswith('!help_admin'):
                    description = '***!set_time [time in minutes]***: set the waiting time, bot will wait this waiting time before continuing with the queue of a title\n' \
                                  '***!duke_time [time in minutes]***: set the waiting time for duke\n' \
                          '***!scientist_time [time in minutes]***: set the waiting time for scientist\n' \
                          '***!architect_time [time in minutes]***: set the waiting time for architect\n' \
                          '***!justice_time [time in minutes]***: set the waiting time for justice\n' \
                          '***!turn_off***: bot will be turned off\n***!turn_on***: bot will be turned on\n' \
                          '***!turn_off duke***: bot will be turned off\n***!turn_on duke***: bot will be turned on\n' \
                                  '***!turn_off scientist***: bot will be turned off\n***!turn_on scientist***: bot will be turned on\n' \
                                  '***!turn_off justice***: bot will be turned off\n***!turn_on justice***: bot will be turned on\n' \
                                  '***!turn_off architect***: bot will be turned off\n***!turn_on architect***: bot will be turned on\n' \
                                  '***!add_blacklist/!add_whitelist*** **[discord_id]**: to add users to blacklist or whitelist. The whitelisted users will skip the queue\n' \
                          '***!remove_blacklist/!remove_whitelist***: to remove users from blacklist/whitelist\n' \
                          '***!hk/!lk***: it will allow users to use command !title [TITLE_NAME] hk/lk [X] [Y]\n' \
                          '***!booster on/off***: it will allow admin to give more time to certain players for training (for instance to t5 players)\n' \
                          '***!booster_set_time [time in minutes]***: set the time for users that are into the t5 booster list\n' \
                          '***!add_booster/!remove_booster*** **[discord_id]**: you will add players to the booster list\n' \
                          'NOTE **How to get discord user id**: https://support.discord.com/hc/en-us/articles/206346498-Where-can-I-find-my-User-Server-Message-ID-'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                elif message.content.split(' ')[0].lower() =='!booster':
                    config = json.load(open('config.json'))
                    params = message.content.split(' ')[1].lower()
                    if params == 'on':
                        booster = True
                    elif params == 'off':
                        booster = False
                    else:
                        booster = False
                    config['booster'] = booster
                    with open('config.json', 'w') as f:
                        json.dump(config, f, indent=1)
                    description = f'Booster is set to: **{booster}**'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                elif message.content.startswith('!booster_set_time'):
                    config = json.load(open('config.json'))
                    params = message.content.split(' ')[1].lower()
                    if len(params) != 0:
                        config['booster_time'] = int(params)*60
                        with open('config.json', 'w') as f:
                            json.dump(config, f, indent=1)
                        description = f'New booster time set to: **{str(params)}** min'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                elif message.content.startswith('!add_booster'):
                    booster = json.load(open('booster.json'))
                    split_message = message.content.split(' ')
                    params = split_message[1]
                    booster.append(params)
                    with open('booster.json', 'w') as f:
                        json.dump(booster, f, indent=1)

                    description = f'User has been added to the booster list: ***{params}***'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                elif message.content.startswith('!remove_booster'):
                    booster = json.load(open('booster.json'))
                    split_message = message.content.split(' ')
                    params = split_message[1]
                    booster.remove(params)
                    with open('booster.json', 'w') as f:
                        json.dump(booster, f, indent=1)
                    description = f'User has been removed from the booster list: ***{params}***'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                elif message.content.startswith('!add_blacklist'):
                    blacklist = json.load(open('blacklist.json'))
                    split_message = message.content.split(' ')
                    params = split_message[1]
                    blacklist.append(params)
                    with open('blacklist.json', 'w') as f:
                        json.dump(blacklist, f, indent=1)

                    description = f'User has been added to the blacklist: ***{params}***'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)

                elif message.content.startswith('!remove_blacklist'):
                    blacklist = json.load(open('blacklist.json'))
                    split_message = message.content.split(' ')
                    command = split_message[0]
                    params = split_message[1]
                    blacklist.remove(params)
                    with open('blacklist.json', 'w') as f:
                        json.dump(blacklist, f, indent=1)
                    description = f'User has been removed from the blacklist: ***{params}***'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                elif message.content.startswith('!add_whitelist'):
                    whitelist = json.load(open('whitelist.json'))
                    split_message = message.content.split(' ')
                    command = split_message[0]
                    params = split_message[1]
                    whitelist.append(params)
                    with open('whitelist.json', 'w') as f:
                        json.dump(whitelist, f, indent=1)
                    description = f'User has been added to the whitelist: ***{params}***'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                elif message.content.startswith('!remove_whitelist'):
                    whitelist = json.load(open('whitelist.json'))
                    split_message = message.content.split(' ')
                    command = split_message[0]
                    params = split_message[1]
                    whitelist.remove(params)
                    with open('whitelist.json', 'w') as f:
                        json.dump(whitelist, f, indent=1)
                    description = f'User has been removed from the whitelist: ***{params}***'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                elif message.content.startswith('!hk'):
                    config = json.load(open('config.json'))
                    split_message = message.content.split(' ')
                    params = split_message[1]
                    config['hk'] = params
                    with open('config.json', 'w') as f:
                        json.dump(config, f, indent=1)
                    description = f'Home kingdom number has been updated to: ***{params}***'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)

                elif message.content.startswith('!lk'):
                    config = json.load(open('config.json'))
                    split_message = message.content.split(' ')
                    command = split_message[0]
                    params = split_message[1]

                    config['lk'] = params
                    with open('config.json', 'w') as f:
                        json.dump(config, f, indent=1)

                    description = f'Lost kingdom number has been updated to: ***{params}***'
                    embed = discord.Embed(
                        description=description,
                        color=discord.Color.red())
                    await message.channel.send(embed=embed)
                elif message.content.startswith('!duke_time'):
                    split_message = message.content.split(' ')
                    params = split_message[1]
                    if len(params) != 0:
                        d = json.load(open('config.json'))
                        d['duke_time'] = int(params)*60
                        with open('config.json', 'w') as f:
                            json.dump(d, f, indent=1)
                        description = f'New waiting time for **duke** set to: **{str(params)}** min'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                elif message.content.startswith('!justice_time'):
                    split_message = message.content.split(' ')
                    params = split_message[1]
                    if len(params) != 0:
                        d = json.load(open('config.json'))
                        d['justice_time'] = int(params)*60
                        with open('config.json', 'w') as f:
                            json.dump(d, f, indent=1)
                        description = f'New waiting time for **justice** set to: **{str(params)}** min'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                elif message.content.startswith('!architect_time'):
                    split_message = message.content.split(' ')
                    params = split_message[1]
                    if len(params) != 0:
                        d = json.load(open('config.json'))
                        d['architect_time'] = int(params)*60
                        with open('config.json', 'w') as f:
                            json.dump(d, f, indent=1)
                        description = f'New waiting time for **architect** set to: **{str(params)}** min'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)
                elif message.content.startswith('!scientist_time'):
                    split_message = message.content.split(' ')
                    params = split_message[1]
                    if len(params) != 0:
                        d = json.load(open('config.json'))
                        d['scientist_time'] = int(params)*60
                        with open('config.json', 'w') as f:
                            json.dump(d, f, indent=1)
                        description = f'New waiting time for **scientist** set to: **{str(params)}** min'
                        embed = discord.Embed(
                            description=description,
                            color=discord.Color.red())
                        await message.channel.send(embed=embed)


        except Exception as e:
            print(e)

    asyncio.create_task(process_queue())
    await client.start(TOKEN)

'FUNCTION TO START BOT'
#asyncio.run(run_discord_bot())