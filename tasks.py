
import cv2
import subprocess
import numpy as np
from time import sleep
import io
import PIL.Image
import numpy
from PIL import Image, ImageEnhance,ImageGrab
import solve_captcha
import random
import json
import pytesseract
pytesseract.pytesseract.tesseract_cmd = "tesocr\\tesseract.exe"
def tap(x,y, n, IP,PORT):
    adb_path = 'adb\\adb.exe'
    for i in range(n):
        subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}', stderr=subprocess.PIPE)
        sleep(1)
    return

def Screenshot(IP,PORT):
    adb_path = 'adb\\adb.exe'
    max_retries = 10
    for i in range(max_retries):
        screenshot = subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} exec-out screencap -p',
                                    stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        try:
            #cv2.imwrite(f'screenshot.png', np.uint8(screenshot.stdout))
            #re_open = PIL.Image.open(f'screenshot.png')
            return PIL.Image.open(io.BytesIO(screenshot.stdout))

        except PIL.UnidentifiedImageError:
            print(f"Failed to capture screenshot on attempt {i + 1}/{max_retries}")
    raise Exception("Failed to capture screenshot after retries.")
def check_game_status(IP,PORT):
    screenshot = Screenshot(IP, PORT)
    screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
    target = cv2.imread('images/game_icon.png', 0)
    h, w = target.shape
    result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location = min_loc
    else:
        location = max_loc
    if max_val > 0.8:
        print('Game not open: opening the game')
        sleep(1)
        tap(location[0], location[1], 1, IP, PORT)
        sleep(40)

def inizialize(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = None
    print('Checking if game is open')
    check_game_status(IP,PORT)
    print('Checking Captcha: 1')
    solve_captcha.check_captcha(IP,PORT)
    print('Sleeping')
    sleep(random.uniform(0.5,2.0))
    print('Checking Captcha: 2')
    solve_captcha.check_captcha(IP, PORT)
    counter = 0
    while ready == None and counter<5:
        counter+=1
        screenshot = Screenshot(IP, PORT)
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/in_city.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc
        target1 = cv2.imread('images/out_of_city.png', 0)
        h, w = target.shape
        result1 = cv2.matchTemplate(screenshot, target1, cv2.TM_CCOEFF_NORMED)
        min_val1, max_val1, min_loc1, max_loc1 = cv2.minMaxLoc(result1)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location1 = min_loc1
        else:
            location1 = max_loc1

        if max_val < 0.7 and max_val1 < 0.7:
            print('Game window not in-city and neither outside of the city: Closing the current window')
            sleep(1)
            subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input keyevent 4', stdout=subprocess.PIPE,
                           stderr=subprocess.PIPE)
            ready = None
        else:
            if max_val1 > 0.7:
                print(max_val1)
                print('Game window: outside of city, going inside')
                tap(location1[0],location1[1],1,IP,PORT)
                print('Game window: inside of city, going outside')
                #inside_city(IP,PORT)
                sleep(7)
                tap(location1[0], location1[1], 1, IP, PORT)
            elif max_val > 0.7:
                #print(max_val)
                print('Game window: inside of city, going outside')
                sleep(2)
                tap(location[0], location[1], 1, IP, PORT)

            ready = True
            print('Ready to start')
        sleep(1)
    sleep(1)
def inside_city(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = False
    counter = 0
    while ready == False:

        counter += 1
        location = Screenshot(IP, PORT)
        testing = cv2.cvtColor(np.array(location), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/in_city.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc
        if max_val > 0.7:
            ready = True
            tap(location[0], location[1], 1, IP, PORT)
        else:
            ready = False
    return ready

def general(IP, PORT):
    ready = False
    counter = 0
    while ready == False and counter < 5:
        counter += 1
        location = Screenshot(IP,PORT)
        #location = PIL.Image.open(io.BytesIO(location.stdout))
        testing = cv2.cvtColor(np.array(location), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/general.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc
        #print(max_val)
        if max_val > 0.7:
            ready = True

        else:
            ready= False
            sleep(3)
    return ready


def prime_minister(IP, PORT):
    ready = False
    counter = 0
    while ready == False and counter < 5:
        counter += 1
        location = Screenshot(IP, PORT)
        # location = PIL.Image.open(io.BytesIO(location.stdout))
        testing = cv2.cvtColor(np.array(location), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/prime_minister.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc
        # print(max_val)
        if max_val > 0.7:
            ready = True

        else:
            ready = False
            sleep(3)
    return ready


def ready_to_give(IP, PORT):
    adb_path = 'adb\\adb.exe'
    ready = False
    counter = 0
    print('Checking for the city')

    # Define the list of target images
    target_images = ['giver_title.png','giver_title1.png', 'giver_title2.png', 'giver_title3.png']
    d = json.load(open('config.json'))
    kd_numb = d['kingdom']
    while ready == False and counter <= 10:
        counter += 1

        ###ADD CODE IF LEN KD > 4 : tap
        ###else: tap
        if len(kd_numb)>4:
            tap(random.randrange(629, 702), random.randrange(339, 397), 1, IP, PORT)
        else:
            tap(random.randrange(481, 794), random.randrange(300, 497), 1, IP, PORT)
        #adb shell input keyevent
        # Iterate over each target image
        for target_image in target_images:
            target = cv2.imread(f'images/{target_image}', 0)
            h, w = target.shape
            location = Screenshot(IP, PORT)
            testing = cv2.cvtColor(np.array(location), cv2.COLOR_RGB2GRAY)
            cv2.imwrite('location_picture.png', testing)
            result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                location = min_loc
            else:
                location = max_loc
            if max_val > 0.9:
                print(f'City found and {target_image} on Bot account')
                ready = True
                tap(location[0]+4, location[1]+4, 1, IP, PORT)
                break  # Exit the loop if any target image is found

        if not ready:
            ready = False
    return ready
def on_target(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = None
    while ready == None:
        screenshot = Screenshot(IP, PORT)
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/on_target_city.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc
        #print(max_val)
        if max_val < 0.7:
            return False
        else:
            if max_val > 0.7:
                #print(location[0], location[1])
                tap(location[0], location[1], 1, IP, PORT)
            ready = True
        sleep(1)
    sleep(1)


with open('player_list.json') as f:
    player_data = json.load(f)

def update_player(player_ds_id, kingdom, x, y):
    player_exists = False
    for player in player_data:
        if player['player_ds_id'] == str(player_ds_id):
            player_exists = True
            if kingdom is not None:
                player['kingdom'] = kingdom
            if x is not None:
                player['x'] = x
            if y is not None:
                player['y'] = y

    if player_exists == False:
        new_player = {
            'player_ds_id': player_ds_id,
            'kingdom': kingdom,
            'x': x,
            'y': y
        }
        player_data.append(new_player)
    with open('player_list.json', 'w') as f:
        json.dump(player_data, f, indent=4)
    return True


def get_player(player_ds_id):
    for player in player_data:
        if player['player_ds_id'] == player_ds_id:
            return player

def check_id(IP, PORT):
    adb_path = 'adb\\adb.exe'
    ready = False
    counter = 0
    print('Checking for the city')
    target_images = ['giver_title.png','giver_title1.png', 'giver_title2.png', 'giver_title3.png']
    d = json.load(open('config.json'))
    kd_numb = d['kingdom']
    while ready == False and counter <= 10:
        counter += 1

        if len(kd_numb) > 4:
            tap(random.randrange(629, 702), random.randrange(339, 397), 1, IP, PORT)
        else:
            tap(random.randrange(481, 794), random.randrange(300, 497), 1, IP, PORT)
        # Iterate over each target image
        for target_image in target_images:
            target = cv2.imread(f'images/{target_image}', 0)
            h, w = target.shape
            location = Screenshot(IP, PORT)
            testing = cv2.cvtColor(np.array(location), cv2.COLOR_RGB2GRAY)
            cv2.imwrite('location_picture.png', testing)
            result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                location = min_loc
            else:
                location = max_loc
            if max_val > 0.9:
                print('Preview opened')
                # Calculate the center coordinates relative to the fixed screen size (1280x720)
                center_x = location[0] + w // 2
                center_y = location[1] + h // 2
                center_x_screen = (center_x * 1280) // testing.shape[1]
                center_y_screen = (center_y * 720) // testing.shape[0]

                # Perform the action to press in the middle of the found image
                tap(center_x_screen + 50, center_y_screen + 130, 1, IP, PORT)
                # tap(location[0], location[1], 1, IP, PORT)
                sleep(2)
                profile_image = Screenshot(IP, PORT)
                id = scan_id(profile_image, IP, PORT).strip()
                print(id)
                blacklist = json.load(open('blacklist.json'))
                if str(id) in blacklist:
                    print('true')
                    tap(1090, 83, 1, IP, PORT)
                    return 'None'
                tap(1090, 83, 1, IP, PORT)

                ready = True
                break  # Exit the loop if any target image is found
        if not ready:
            ready = False
    if not ready:
        screenshot = Screenshot(IP,PORT)
        #pil_image = ImageGrab.grab(bbox=(0, 0, screenshot.width, screenshot.height))
        #numpy_array = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        #numpy_array_bgr = cv2.imread('city_not_found.png', 0)
        numpy_array_rgb = cv2.cvtColor(numpy.uint8(screenshot), cv2.COLOR_BGR2RGB)
        cv2.imwrite('city_not_found.png', numpy_array_rgb)
    return ready




def scan_id(profile_image, IP, PORT):
    id_box = (618, 187, 730, 211)
    id_image = profile_image.crop(id_box)
    img_gray = cv2.cvtColor(numpy.uint8(id_image), cv2.COLOR_BGR2GRAY)
    id = pytesseract.image_to_string(img_gray, config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789')

    counter = 0
    while id == '' and counter <= 5:
        sleep(1)
        new_screenshot = Screenshot(IP,PORT)
        id_box = (585, 155, 680, 180)
        new_id_image = new_screenshot.crop(id_box)
        print('first attempt empty')
        cv2.imwrite(f'temp.png', np.uint8(new_id_image))
        re_open = Image.open(f'temp.png').convert('L')
        enhancer = ImageEnhance.Contrast(re_open)
        re_open = enhancer.enhance(15.0)
        id = pytesseract.image_to_string(re_open, config='--psm 6 --oem 3 -c tessedit_char_whitelist=0123456789')

        counter += 1
    if id == '':
        print('still empty')
    return id

def check_duke(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = False
    print('Checking for the title')
    location = Screenshot(IP, PORT)
    testing = cv2.cvtColor(np.array(location), cv2.COLOR_RGB2GRAY)
    target = cv2.imread('images/duke_given.png', 0)
    h, w = target.shape
    result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location = min_loc
    else:
        location = max_loc
    if max_val > 0.9:
        print(max_val)
        print('Title on user')
        ready =  True
    else:
        ready =  False
    return ready

def check_architect(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = False
    print('Checking for the title')
    location = Screenshot(IP, PORT)
    testing = cv2.cvtColor(np.array(location), cv2.COLOR_RGB2GRAY)
    target = cv2.imread('images/architect_given.png', 0)
    h, w = target.shape
    result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location = min_loc
    else:
        location = max_loc
    if max_val > 0.9:
        print(max_val)
        print('Title on user')
        ready =  True
    else:
        ready =  False
    return ready

def check_justice(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = False
    print('Checking for the title')
    location = Screenshot(IP, PORT)
    testing = cv2.cvtColor(np.array(location), cv2.COLOR_RGB2GRAY)
    target = cv2.imread('images/justice_given.png', 0)
    h, w = target.shape
    result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location = min_loc
    else:
        location = max_loc
    if max_val > 0.9:
        print(max_val)
        print('Title on user')
        ready =  True
    else:
        ready =  False
    return ready

def check_scientist(IP,PORT):
    adb_path = 'adb\\adb.exe'
    ready = False
    print('Checking for the title')
    location = Screenshot(IP, PORT)
    testing = cv2.cvtColor(np.array(location), cv2.COLOR_RGB2GRAY)
    target = cv2.imread('images/scientist_given.png', 0)
    h, w = target.shape
    result = cv2.matchTemplate(testing, target, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location = min_loc
    else:
        location = max_loc
    if max_val > 0.9:
        print(max_val)
        print('Title on user')
        ready =  True
    else:
        ready =  False
    return ready