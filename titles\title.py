import adb
import solve_captcha
import tasks
import subprocess
from time import sleep
import json

adb_path = 'adb\\adb.exe'
def clear_input(IP, PORT):
    subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input keyevent KEYCODE_MOVE_END', stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)

    subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input keyevent KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL KEYCODE_DEL',
                   stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)

def start_adb_server():
    subprocess.run([f'{adb_path}', '-P', '5038', 'start-server'])

def connect_adb():
    d = json.load(open('config.json'))
    ip = d['ip']
    port = d['port']
    IP_PORT = str(ip) + ':' + str(port)
    subprocess.run(f'{adb_path} -P 5038 connect {IP_PORT}', stdout=subprocess.PIPE, stderr=subprocess.PIPE)

def reboot():
    # Load IP and port from config.json
    with open('config.json') as config_file:
        config = json.load(config_file)
        ip = config['ip']
        port = config['port']
        exe_path = config['exe_path']
    # Define IP:PORT string
    ip_port = f"{ip}:{port}"

    # Define the ADB command with the specified port
    adb_command = f"{adb_path} -P 5038 -s {ip_port} reboot"

    # Run the ADB command
    result = subprocess.run(adb_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)

    # Check the command's output
    if result.returncode == 0:
        print("ADB command executed successfully.")
        sleep(2)

        # Open the .lnk file
        subprocess.Popen(['cmd', '/c', exe_path])
        sleep(60)
        d = json.load(open('config.json'))
        d['working'] = False
        with open('config.json', 'w') as f:
            json.dump(d, f, indent=1)
    else:
        print("Error executing ADB command:", result.stderr.decode())

def common_steps(kd_numb, x,y, IP, PORT):

    IP_PORT = str(IP) + ':' + str(PORT)
    print('Connecting to adb')
    connect_adb()
    print('Connected')
    print('Executing the inizialization')
    tasks.inizialize(IP,PORT)
    #Tap on coordinates
    tasks.tap(332,23,1,IP,PORT)
    # tap on kingdom number for input
    tasks.tap(444, 142, 1, IP, PORT)
    #cleanin kingdom number input
    clear_input(IP,PORT)
    # input kingdom number
    print('Cleaning entry boxes')
    subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input text {kd_numb.strip()}', stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)

    #tap on X coordinates  for input
    tasks.tap(637, 142, 2, IP, PORT)
    #tasks.tap(637, 142, 1, IP, PORT)

    #input x coordinate
    subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input text {x.strip()}', stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)
    sleep(2)
    #tap on y coordinate for input
    tasks.tap(778, 140, 2, IP, PORT)
    #input y coordinate
    subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input text {y.strip()}', stdout=subprocess.PIPE,
                   stderr=subprocess.PIPE)
    sleep(2)
    #tap on go to coordinates
    tasks.tap(885, 143, 2, IP, PORT)
    print('Waiting for window to load')
    sleep(5)
    solve_captcha.check_captcha(IP, PORT)
    #open_window = tasks.on_target(IP,PORT)
    print('Checking if city is found')
    check_gov_id = tasks.check_id(IP,PORT)
    if check_gov_id == 'None':
        return 'None'
    found = tasks.ready_to_give(IP,PORT)
    if found == False:
        print('City not found')
        return found
    #print(condition)

def duke(kd_numb, x,y, IP, PORT):
    print('Setting Duke params and executing common steps')
    found = common_steps(kd_numb, x,y, IP, PORT)
    if found == False:
        return found
    elif found == 'None':
        return found

    #CHECK IF TITLE IS ALREADY ON HIM
    check_duke = tasks.check_duke(IP,PORT)
    #print(check_duke)
    counter = 0
    if check_duke == True:
        sleep(1)
        tasks.tap(640, 636, 1, IP, PORT)
        return 'On you'
    else:
        while check_duke == False and counter <=5:
            counter += 1
            tasks.tap(520, 396, 1, IP, PORT)
            sleep(1)
            check_duke = tasks.check_duke(IP, PORT)
        if check_duke == False:
            return 'False'
        tasks.tap(640, 636, 1, IP, PORT)
        sleep(1)
        print('Title has been given')
    solve_captcha.check_captcha(IP, PORT)
    tasks.tap(75, 664, 1, IP, PORT)
    sleep(2)
    return 'Done'

def architect(kd_numb, x, y, IP, PORT):
    print('Setting Architect params and executing common steps')
    found = common_steps(kd_numb, x, y, IP, PORT)
    if found == False:
        return found
    elif found == 'None':
        return found
    check_architect = tasks.check_architect(IP, PORT)
    # print(check_duke)
    counter = 0
    if check_architect == True:
        sleep(1)
        tasks.tap(640, 636, 1, IP, PORT)
        return 'On you'
    else:
        while check_architect == False and counter <=5:
            counter += 1
            tasks.tap(748, 395, 1, IP, PORT)
            sleep(1)
            check_architect = tasks.check_architect(IP, PORT)
        if check_architect == False:
            return 'False'
        tasks.tap(640, 636, 1, IP, PORT)
        sleep(1)
        print('Title has been given')
    solve_captcha.check_captcha(IP, PORT)
    tasks.tap(75, 664, 1, IP, PORT)
    sleep(2)
    return 'Done'

def justice(kd_numb, x,y, IP, PORT):
    print('Setting Justice params and executing common steps')
    found =  common_steps(kd_numb, x, y, IP, PORT)
    if found == False:
        return found
    elif found == 'None':
        return found
    check_justice = tasks.check_justice(IP, PORT)
    # print(check_duke)
    counter = 0
    if check_justice == True:

        sleep(1)
        tasks.tap(640, 636, 1, IP, PORT)
        return 'On you'
    else:
        while check_justice == False and counter <=5:
            counter += 1
            tasks.tap(291, 395, 1, IP, PORT)
            sleep(1)
            check_justice = tasks.check_justice(IP, PORT)
        if check_justice == False:
            return 'False'
        tasks.tap(640, 636, 1, IP, PORT)
        sleep(1)
        print('Title has been given')
    solve_captcha.check_captcha(IP, PORT)
    tasks.tap(75, 664, 1, IP, PORT)
    sleep(2)
    return 'Done'


def scientist(kd_numb, x,y, IP, PORT):
    print('Setting Scientist params and executing common steps')
    found = common_steps(kd_numb, x, y, IP, PORT)
    if found == False:
        return found
    elif found == 'None':
        return found
    check_scientist = tasks.check_scientist(IP, PORT)
    # print(check_duke)
    counter = 0
    if check_scientist == True:
        sleep(1)
        tasks.tap(640, 636, 1, IP, PORT)
        return 'On you'
    else:
        while check_scientist == False and counter <=5:
            counter += 1
            tasks.tap(979, 395, 1, IP, PORT)
            sleep(1)
            check_scientist = tasks.check_scientist(IP, PORT)
        if check_scientist == False:
            return 'False'
        tasks.tap(640, 636, 1, IP, PORT)
        sleep(1)
        print('Title has been given')
    solve_captcha.check_captcha(IP, PORT)
    tasks.tap(75, 664, 1, IP, PORT)
    sleep(2)
    return 'Done'
