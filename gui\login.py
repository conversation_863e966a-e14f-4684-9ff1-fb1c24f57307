import os

from tkinter import *
from tkinter import ttk, messagebox
import json
from datetime import datetime
from keyauth import api
import sys
import hashlib
from PIL import ImageTk
from KeyPage import KeyPage
import PIL.Image
import requests
import subprocess
import gui.giver_gui, gui.Register, gui.Contact

__author__ = 'ROKStats'
__copyright__ = ''
__license__ = ''
__version__ = '1.35'
__maintainer__ = 'ROKStats'
__email__ = ''
__status__ = 'Beta'
_AppName_ = 'ROKStats'

class LoginPage:
    def __init__(self,window):
        d = {}
        def check_login():
            username = self.username_entry.get()
            password = self.password_entry.get()

            d['username'] = self.username_entry.get()
            d['password'] = self.password_entry.get()
            with open('config.json', 'w') as outfile:
                outfile.write(json.dumps(d, indent=1))

            def getchecksum():
                md5_hash = hashlib.md5()
                file = open(''.join(sys.argv), "rb")
                md5_hash.update(file.read())
                digest = md5_hash.hexdigest()
                return digest

            # Bypass KeyAuth initialization for testing
            # keyauthapp = api(
            #     name="ROKScan",
            #     ownerid="Ykbj23aFHl",
            #     secret="",
            #     version="1.0",
            #     hash_to_check=getchecksum()
            # )

            # Bypass KeyAuth for testing - remove this when you set up your own KeyAuth
            msg = 'success'  # keyauthapp.login(username, password)
            #.strftime('%Y-%m-%d %H:%M:%S')
            if msg == 'success':

                try:
                    # Bypass KeyAuth expiry for testing
                    expire = "No expiry (testing mode)"  # datetime.utcfromtimestamp(int(keyauthapp.user_data.expires))
                except:
                    expire = 'lifetime license'
                pass
                gui.giver_gui.giver(window,expire, username, password)
            else:
                if msg == 'No active subscriptions found.':
                    KeyPage(window)
                else:
                    self.fail_label = Label(self.lgn_frame, text=f"{msg}", bg="#040405", fg="#dc143c",
                                                font=("yu gothic ui", 12, "bold"))
                    self.fail_label.place(x=100, y=275)
                    self.fail_label.after(3000, lambda: self.fail_label.destroy())

        def Register_event():
            gui.Register.RegisterPage(window)

        def contact_event():
            gui.Contact.ContactPage(window)

        self.window = window
        self.window.geometry('800x700')
        self.window.resizable(0, 0)
        #self.window.state('zoomed')
        self.window.title('Login Page')
        self.bg_frame = PIL.Image.open('images\\background1.png')
        photo = ImageTk.PhotoImage(self.bg_frame)
        self.bg_panel = Label(self.window, image=photo)
        self.bg_panel.image = photo
        self.bg_panel.pack(fill='both', expand='yes')

        self.lgn_frame = Frame(self.window, bg='#040405', width=500, height=500)
        self.lgn_frame.place(x=150, y=70)
        self.get_key_button = Button(self.lgn_frame, text="press to get a free trial key",
                                    font=("yu gothic ui", 10, "bold underline"), fg="white", relief=FLAT,
                                    activebackground="#040405"
                                    , borderwidth=0, background="#040405", cursor="hand2",command=contact_event)
        self.get_key_button.place(x=140, y=450)


        ##CONTACT BUTTON
        self.cnt_button = PIL.Image.open('images\\contact.png')
        cnt_photo = ImageTk.PhotoImage(self.cnt_button.resize((230,60)))
        self.cnt_button_label = Label(self.lgn_frame, image=cnt_photo, bg='#040405')
        self.cnt_button_label.image = cnt_photo
        self.cnt_button_label.place(x=115, y=300)

        self.contact = Button(self.cnt_button_label, text='CONTACT', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#7F7F7F', cursor='hand2', activebackground='#7F7F7F', fg='#bdb9b1', command= contact_event)
        self.contact.place(x=55, y=12)

        self.txt = "ROKSTATS - Features"
        self.heading = Label(self.lgn_frame, text=self.txt, font=('yu gothic ui', 22, "bold"), bg="#040405",
                             fg='white',
                             bd=5,
                             relief=FLAT)
        self.heading.place(x=100, y=10, width=300, height=30)

        self.sign_in_image = PIL.Image.open('images\\hyy.png')
        photo = ImageTk.PhotoImage(self.sign_in_image.resize((40,30)))

        self.sign_in_image_label = Label(self.lgn_frame, image=photo, bg='#040405')
        self.sign_in_image_label.image = photo
        self.sign_in_image_label.place(x=50, y=130)

        self.sign_in_label = Label(self.lgn_frame, text="Login", bg="#040405", fg="white",
                                    font=("yu gothic ui", 18, "bold"))
        self.sign_in_label.place(x=100, y=70)

        self.username_label = Label(self.lgn_frame, text="Username", bg="#040405", fg="#4f4e4d",
                                    font=("yu gothic ui", 13, "bold"))
        self.username_label.place(x=100, y=105)
        d = json.load(open('config.json'))
        try:
            d['password']
        except Exception as e:
            print(e)
            d['password'] = ""
        self.username_entry = Entry(self.lgn_frame,highlightthickness=0, relief=FLAT, bg="#040405", fg="#6b6a69",
                                    font=("yu gothic ui ", 12, "bold"),insertbackground="white")
        self.username_entry.insert(0,d['username'])
        self.username_entry.place(x=100, y=130, width=270)


        self.username_line = Canvas(self.lgn_frame, width=300, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.username_line.place(x=100, y=160)
        ##LOGIN BUTTON
        self.start_button = PIL.Image.open('images\\btn1.png')
        start_photo = ImageTk.PhotoImage(self.start_button.resize((230,60)))
        self.start_button_label = Label(self.lgn_frame,highlightbackground="black", image=start_photo, bg='#040405')
        self.start_button_label.image = start_photo
        self.start_button_label.place(x=115, y=220)
        self.start = Button(self.start_button_label, text='LOGIN', font=("yu gothic ui", 11, "bold"), width=11, bd=0,
                            bg='#C3C3C3', cursor='hand2', activebackground='#C3C3C3', fg='black', command =check_login)
        self.start.place(x=55, y=12)


        self.log_in_button = Button(self.lgn_frame, text="REGISTER",
                                    font=("yu gothic ui", 10, "bold underline"), fg="white", relief=FLAT,
                                    activebackground="#040405"
                                    , borderwidth=0, background="#040405", cursor="hand2",command=Register_event)
        self.log_in_button.place(x=215, y=50)

        self.password_label = Label(self.lgn_frame, text="Password", bg="#040405", fg="#4f4e4d",
                                    font=("yu gothic ui", 12, "bold"))
        self.password_label.place(x=100, y=165)

        self.password_entry = Entry(self.lgn_frame, highlightthickness=0, relief=FLAT, bg="#040405", fg="#6b6a69",
                                    font=("yu gothic ui", 12, "bold"), show="*",insertbackground="white")
        self.password_entry.insert(0, d['password'])
        self.password_entry.place(x=100, y=190, width=244)

        self.password_line = Canvas(self.lgn_frame, width=300, height=2.0, bg="#bdb9b1", highlightthickness=0)
        self.password_line.place(x=100, y=210)

        self.password_icon = PIL.Image.open('images\\password_icon.png')
        photo = ImageTk.PhotoImage(self.password_icon.resize((30,25)))
        self.password_icon_label = Label(self.lgn_frame, image=photo, bg='#040405')
        self.password_icon_label.image = photo
        self.password_icon_label.place(x=55, y=185)


        link = "https://raw.githubusercontent.com/petruleon/ROKStats-features/main/version.txt"
        check = requests.get(link)
        current_path = os.getcwd()
        if float(__version__) < float(check.text):
            mb1 = messagebox.askyesno('Update Available', 'There is an update available. Click yes to update.')
            if mb1 is True:
                print('yes')
                subprocess.Popen([current_path + '\\Updater.exe', '/user:Administrator'])
                self.window.destroy()


        #r = requests.get("https://raw.githubusercontent.com/petruleon/ROKStats/main/version.txt")
        #current_version = r.text
        #if current_version.replace('\n','') != __version__:
        #    messagebox.showinfo('Software Update', 'Update Available!')
        #    mb1 = messagebox.askyesno('Update!', f'{_AppName_} {__version__} needs to update to version {current_version}')
        #    if mb1 is True:
        #        download_link = requests.get('https://raw.githubusercontent.com/petruleon/ROKStats/main/downloader_link.txt').text
        #        webbrowser.open_new_tab(download_link)
        #        self.window.destroy()