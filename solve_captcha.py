import sys
import os
import cv2
import numpy as np
import subprocess
from time import sleep
import tasks
from PIL import Image
import json

def solve_captcha():
    print('sent for solving')
    sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
    from twocaptcha import TwoCaptcha
    api_key = os.getenv('APIKEY_2CAPTCHA', 'cb0f9e00efc82b1d00a957f032ef959d')
    solver = TwoCaptcha(api_key,defaultTimeout=120, pollingInterval=5)
    try:
        result = solver.coordinates('captcha.jpg',
                                    lang='en')
    except Exception as e:
        sys.exit(e)
    else:
        print('answer received: ',result['code'])
        return result['code']

def check_captcha(IP,PORT):
    print('checking for captcha')
    ready = False

    screenshot = tasks.Screenshot(IP,PORT)
    screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)

    target1 = cv2.imread('images/verification_chest1.png', 0)
    h, w = target1.shape
    result1 = cv2.matchTemplate(screenshot, target1, cv2.TM_CCOEFF_NORMED)
    min_val1, max_val1, min_loc1, max_loc1 = cv2.minMaxLoc(result1)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location1 = min_loc1
    else:
        location1 = max_loc1
    target2 = cv2.imread('images/verification_chest2.png', 0)
    h, w = target2.shape
    result2 = cv2.matchTemplate(screenshot, target2, cv2.TM_CCOEFF_NORMED)
    min_val2, max_val2, min_loc2, max_loc2= cv2.minMaxLoc(result2)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location2 = min_loc2
    else:
        location2 = max_loc2
    target3 = cv2.imread('images/verification_chest3.png', 0)
    h, w = target3.shape
    result3 = cv2.matchTemplate(screenshot, target3, cv2.TM_CCOEFF_NORMED)
    min_val3, max_val3, min_loc3, max_loc3 = cv2.minMaxLoc(result3)
    if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        location3 = min_loc3
    else:
        location3 = max_loc3
    if max_val1 > 0.6 or max_val2 > 0.6 or max_val3 > 0.6:
        d = json.load(open('config.json'))
        d['working'] = True
        with open('config.json', 'w') as f:
            json.dump(d, f, indent=1)
        adb_path = 'adb\\adb.exe'
        subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {location1[0]} {location1[1]}', stderr=subprocess.PIPE)
        check_window(IP, PORT)
    else:

        return False

def check_window(IP, PORT):
    print('captcha found')
    ready = False
    adb_path = 'adb\\adb.exe'
    while ready == False:
        screenshot = tasks.Screenshot(IP,PORT)
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/captcha2.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc
        if max_val > 0.7:
            ready = True
            subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {location[0]} {location[1]}', stderr=subprocess.PIPE)
            sleep(1)
            ready_to_send(IP,PORT)
        else:
            sleep(10)
            ready_to_send(IP, PORT)



def ready_to_send(IP,PORT):
    print('getting ready to send for solving')
    ready = False
    adb_path = 'adb\\adb.exe'
    while ready == False:
        screenshot = tasks.Screenshot(IP, PORT)
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2GRAY)
        target = cv2.imread('images/captcha3.png', 0)
        h, w = target.shape
        result = cv2.matchTemplate(screenshot, target, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        if cv2.TM_CCOEFF_NORMED in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
            location = min_loc
        else:
            location = max_loc

        if max_val > 0.8:
            try:
                screenshot = tasks.Screenshot(IP,PORT)
                screenshot_cv = cv2.UMat(np.array(screenshot))
                #gray_img = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
                #cv2.imwrite('captcha_gray.png', gray_img)
                cv2.imwrite('captcha.jpg',screenshot_cv)
                screenshot = Image.open('captcha.jpg')
                cropped_image = screenshot.crop((436,102,838,619))
                cv2.imwrite('captcha.jpg', cv2.cvtColor(np.array(cropped_image), cv2.COLOR_RGB2BGR))
                coordinates = solve_captcha()
                coordinates = coordinates.replace('coordinates:','').replace('x=','').replace('y=','').split(';')
                print('solving captcha')
                width_i, height_i =1280, 720
                width_o, height_o = cropped_image.size
                width_scaling_factor = width_i / width_o
                height_scaling_factor = height_i / height_o
                for coord in coordinates:
                    x = int(coord.split(',')[0]) + 436
                    y = int(coord.split(',')[1]) + 102
                    x_scaled = x*width_scaling_factor
                    y_scaled = y*height_scaling_factor
                    print(x,x_scaled,y,y_scaled)
                    subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {x} {y}',
                                   stderr=subprocess.PIPE)
                    sleep(1)
                sleep(2)
                subprocess.run(f'{adb_path} -P 5038 -s {str(IP).strip()}:{PORT} shell input tap {location[0]} {location[1]}',
                               stderr=subprocess.PIPE)
                ready = True
                sleep(5)
                check_captcha(IP,PORT)
            except Exception as e:
                print(e)
        else:
            sleep(1)
        d = json.load(open('config.json'))
        d['working'] = False
        with open('config.json', 'w') as f:
            json.dump(d, f, indent=1)






